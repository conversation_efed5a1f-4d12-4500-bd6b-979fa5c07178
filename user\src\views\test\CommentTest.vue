<template>
  <div class="comment-test">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="评论功能测试"
      left-arrow
      @click-left="$router.back()"
      class="test-navbar"
    />

    <!-- 测试说明 -->
    <div class="test-info">
      <van-cell-group title="评论功能测试">
        <van-cell title="测试目标" value="验证评论的标签高亮和提交功能" />
        <van-cell title="测试内容" value="输入包含#标签#和@用户名的评论" />
        <van-cell title="期望效果" value="标签显示蓝色，用户名显示橙色，可点击" />
      </van-cell-group>
    </div>

    <!-- 模拟评论列表 -->
    <div class="comment-section">
      <div class="section-title">
        <h3>评论列表</h3>
        <span class="comment-count">{{ comments.length }}条评论</span>
      </div>

      <div class="comment-list">
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <van-image
            :src="comment.user.avatar"
            round
            width="32"
            height="32"
            fit="cover"
            class="comment-avatar"
          />
          <div class="comment-content">
            <div class="comment-user">{{ comment.user.nickname }}</div>
            <div class="comment-text" v-html="processCommentContent(comment.content)" @click="handleContentClick"></div>
            <div class="comment-meta">
              <span class="comment-time">{{ comment.time }}</span>
              <div class="comment-actions">
                <div class="comment-like" @click="handleCommentLike(comment)">
                  <van-icon
                    :name="comment.isLiked ? 'like' : 'like-o'"
                    :class="{ active: comment.isLiked }"
                    size="14"
                  />
                  <span v-if="comment.likeCount > 0">{{ comment.likeCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加评论按钮 -->
      <div class="add-comment">
        <van-button
          type="primary"
          @click="showCommentInput = true"
          block
          round
        >
          写评论
        </van-button>
      </div>
    </div>

    <!-- 评论输入框 -->
    <van-popup
      v-model="showCommentInput"
      position="bottom"
      :style="{ height: '50%' }"
    >
      <div class="comment-input-container">
        <div class="comment-input-header">
          <span>写评论</span>
          <van-icon name="cross" @click="showCommentInput = false" />
        </div>
        <div class="comment-input-body">
          <van-field
            v-model="commentText"
            type="textarea"
            placeholder="说点什么...支持#标签#和@用户提及"
            rows="4"
            autosize
            maxlength="500"
            show-word-limit
            @input="onCommentInput"
          />
          <!-- 实时预览评论内容 -->
          <div v-if="commentText.trim()" class="comment-preview">
            <div class="preview-label">预览：</div>
            <div class="preview-content" v-html="processCommentContent(commentText)" @click="handleContentClick"></div>
          </div>
        </div>
        <div class="comment-input-footer">
          <div class="comment-input-tips">
            <span class="tip-item">支持 #标签# 和 @用户名</span>
          </div>
          <van-button
            type="primary"
            @click="submitComment"
            :disabled="!commentText.trim()"
            block
          >
            发布评论
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <van-cell-group title="测试结果">
        <van-cell title="最后提取的标签" :value="lastExtractedTags.join(', ') || '无'" />
        <van-cell title="最后提取的用户" :value="lastExtractedMentions.join(', ') || '无'" />
        <van-cell title="点击测试" value="点击评论中的蓝色标签和橙色用户名" />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 响应式数据
const showCommentInput = ref(false)
const commentText = ref('')
const lastExtractedTags = ref([])
const lastExtractedMentions = ref([])

// 模拟评论数据
const comments = ref([
  {
    id: 1,
    user: {
      nickname: '测试用户1',
      avatar: 'https://via.placeholder.com/32x32/4CAF50/FFFFFF?text=U1'
    },
    content: '这张照片真不错！#风景# #摄影# 和 @小明 一起拍的吗？',
    time: '2分钟前',
    isLiked: false,
    likeCount: 3
  },
  {
    id: 2,
    user: {
      nickname: '测试用户2',
      avatar: 'https://via.placeholder.com/32x32/2196F3/FFFFFF?text=U2'
    },
    content: '太美了！#旅行# #海边# @小红 你也应该来看看',
    time: '5分钟前',
    isLiked: true,
    likeCount: 8
  },
  {
    id: 3,
    user: {
      nickname: '测试用户3',
      avatar: 'https://via.placeholder.com/32x32/FF9800/FFFFFF?text=U3'
    },
    content: '喜欢这种 #自然# 的感觉，@摄影师 拍得很棒！',
    time: '10分钟前',
    isLiked: false,
    likeCount: 5
  }
])

// 处理评论内容的标签和@用户高亮显示
const processCommentContent = (content) => {
  if (!content) return ''

  let processedContent = content

  // 处理标签 #标签名称# 格式，显示为蓝色可点击
  processedContent = processedContent.replace(/#([^#]+)#/g, '<span class="tag-highlight" data-tag="$1">#$1#</span>')

  // 处理@用户提及 @用户昵称 格式，显示为橙色可点击
  processedContent = processedContent.replace(/@([^\s@]+)/g, '<span class="mention-highlight" data-mention="$1">@$1</span>')

  return processedContent
}

// 评论输入处理
const onCommentInput = (value) => {
  console.log('评论输入内容:', value)
}

// 提取评论中的标签和@用户提及
const extractCommentData = (content) => {
  const tags = []
  const mentions = []

  // 提取标签
  const tagMatches = content.match(/#([^#]+)#/g)
  if (tagMatches) {
    tagMatches.forEach(match => {
      const tagName = match.slice(1, -1) // 去掉前后的#号
      if (tagName && !tags.includes(tagName)) {
        tags.push(tagName)
      }
    })
  }

  // 提取@用户提及
  const mentionMatches = content.match(/@([^\s@]+)/g)
  if (mentionMatches) {
    mentionMatches.forEach(match => {
      const username = match.slice(1) // 去掉@号
      if (username && !mentions.includes(username)) {
        mentions.push(username)
      }
    })
  }

  return { tags, mentions }
}

// 提交评论
const submitComment = () => {
  if (!commentText.value.trim()) {
    showToast('请输入评论内容')
    return
  }

  // 提取评论中的标签和@用户提及
  const { tags, mentions } = extractCommentData(commentText.value.trim())
  console.log('评论中提取的标签:', tags)
  console.log('评论中提取的@用户提及:', mentions)

  // 更新测试结果
  lastExtractedTags.value = tags
  lastExtractedMentions.value = mentions

  // 添加到评论列表
  const newComment = {
    id: comments.value.length + 1,
    user: {
      nickname: '当前用户',
      avatar: 'https://via.placeholder.com/32x32/E91E63/FFFFFF?text=ME'
    },
    content: commentText.value.trim(),
    time: '刚刚',
    isLiked: false,
    likeCount: 0
  }

  comments.value.unshift(newComment)
  commentText.value = ''
  showCommentInput.value = false
  showToast('评论发布成功')
}

// 处理标签点击
const handleTagClick = (tagName) => {
  console.log('点击标签:', tagName)
  showToast(`点击了标签: ${tagName}`)
}

// 处理@用户提及点击
const handleMentionClick = (username) => {
  console.log('点击用户提及:', username)
  showToast(`点击了用户: ${username}`)
}

// 内容点击处理函数
const handleContentClick = (event) => {
  const target = event.target
  console.log('点击了内容元素:', target)

  // 处理标签点击
  if (target.classList.contains('tag-highlight')) {
    const tagName = target.getAttribute('data-tag')
    console.log('点击标签:', tagName)
    if (tagName) {
      handleTagClick(tagName)
    }
  }

  // 处理用户提及点击
  if (target.classList.contains('mention-highlight')) {
    const username = target.getAttribute('data-mention')
    console.log('点击用户提及:', username)
    if (username) {
      handleMentionClick(username)
    }
  }
}

// 点赞评论
const handleCommentLike = (comment) => {
  comment.isLiked = !comment.isLiked
  comment.likeCount = comment.isLiked
    ? comment.likeCount + 1
    : Math.max(comment.likeCount - 1, 0)
  
  showToast(comment.isLiked ? '点赞成功' : '取消点赞')
}

onMounted(() => {
  console.log('评论功能测试页面已挂载')
})
</script>

<style scoped>
.comment-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.test-info {
  margin: 16px;
}

.comment-section {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.comment-count {
  font-size: 12px;
  color: #999;
}

.comment-list {
  padding: 0 16px;
}

.comment-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-user {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.comment-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.comment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-like {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.comment-like:hover {
  background-color: #f5f5f5;
}

.comment-like .van-icon {
  color: #666;
  transition: color 0.2s;
}

.comment-like .van-icon.active {
  color: #ff4757;
}

.comment-like span {
  font-size: 12px;
  color: #666;
}

.add-comment {
  padding: 16px;
}

.test-results {
  margin: 16px;
}

/* 评论输入框样式 */
.comment-input-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comment-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 600;
}

.comment-input-body {
  flex: 1;
  padding: 16px;
}

.comment-input-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 评论预览样式 */
.comment-preview {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #1989fa;
}

.preview-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 评论输入提示 */
.comment-input-tips {
  margin-bottom: 12px;
  text-align: center;
}

.tip-item {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

/* 标签和用户提及高亮样式 */
.comment-text :deep(.tag-highlight),
.preview-content :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.comment-text :deep(.tag-highlight:hover),
.preview-content :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.comment-text :deep(.mention-highlight),
.preview-content :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.comment-text :deep(.mention-highlight:hover),
.preview-content :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}
</style>
