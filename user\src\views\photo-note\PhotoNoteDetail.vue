<template>
  <div class="photo-note-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="照片笔记"
      left-arrow
      @click-left="$router.back()"
      class="detail-navbar"
    >
      <template #right>
        <van-icon name="share-o" @click="shareNote" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#1989fa" vertical class="loading-container">
      加载中...
    </van-loading>

    <!-- 照片笔记内容 -->
    <div v-if="!loading && noteDetail" class="note-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <van-image
          :src="noteDetail.avatar"
          round
          width="40"
          height="40"
          fit="cover"
          class="user-avatar"
          @click="goToUserProfile"
        />
        <div class="user-details">
          <div class="user-name" @click="goToUserProfile">{{ noteDetail.nickname }}</div>
          <div class="publish-time">{{ formatTime(noteDetail.createdAt) }}</div>
        </div>
        <!-- 关注按钮 -->
        <van-button
          v-if="isLoggedIn && !isOwnNote"
          :type="isFollowing ? 'default' : 'primary'"
          size="small"
          round
          @click="handleFollow"
          class="follow-btn"
        >
          {{ isFollowing ? '已关注' : '关注' }}
        </van-button>
      </div>

      <!-- 标题和内容 - PC端在照片上方显示 -->
      <div class="content-section content-section-top">
        <h2 v-if="noteDetail.title" class="note-title">{{ noteDetail.title }}</h2>
        <div class="note-content-text" v-html="processedContent" @click="handleContentClick"></div>
      </div>

      <!-- 照片展示 -->
      <div class="photo-section">
        <div class="photo-grid" :class="getPhotoGridClass">
          <div
            v-for="(image, index) in noteDetail.images"
            :key="image.photoId"
            class="photo-item"
            @click="previewPhoto(index)"
          >
            <van-image
              :src="privateImageUrls[index] || image.thumbnailUrl || image.url"
              fit="cover"
              width="100%"
              height="100%"
              :alt="`照片${index + 1}`"
            >
              <template #loading>
                <van-loading type="spinner" size="20" />
              </template>
              <template #error>
                <div class="error-placeholder">
                  <van-icon name="photo-fail" size="24" />
                </div>
              </template>
            </van-image>
          </div>
        </div>
      </div>

      <!-- 标题和内容 - 移动端在照片下方显示 -->
      <div class="content-section content-section-bottom">
        <h2 v-if="noteDetail.title" class="note-title">{{ noteDetail.title }}</h2>
        <div class="note-content-text" v-html="processedContent" @click="handleContentClick"></div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <div class="action-item" @click="handleLike">
            <van-icon
              :name="noteDetail.isLiked ? 'like' : 'like-o'"
              :class="{ active: noteDetail.isLiked }"
              size="20"
            />
            <span class="action-text">{{ noteDetail.likeCount || 0 }}</span>
          </div>
          <div class="action-item" @click="handleCollect">
            <van-icon
              :name="noteDetail.isCollected ? 'star' : 'star-o'"
              :class="{ active: noteDetail.isCollected }"
              size="20"
            />
            <span class="action-text">{{ noteDetail.collectCount || 0 }}</span>
          </div>
          <div class="action-item" @click="showCommentBox">
            <van-icon name="chat-o" size="20" />
            <span class="action-text">{{ noteDetail.commentCount || 0 }}</span>
          </div>
          <div class="action-item" @click="shareNote">
            <van-icon name="share-o" size="20" />
            <span class="action-text">分享</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <van-empty v-else-if="!loading && !noteDetail" description="照片笔记不存在或已被删除" />

    <!-- 评论区域 -->
    <div v-if="!loading && noteDetail" class="comment-section">
      <div class="comment-header">
        <h3>评论 ({{ noteDetail.commentCount || 0 }})</h3>
      </div>

      <!-- 评论列表 -->
      <div class="comment-list">
        <div v-if="commentLoading && comments.length === 0" class="comment-loading">
          <van-loading type="spinner" size="20" />
          <span>加载评论中...</span>
        </div>

        <div v-else-if="comments.length === 0" class="no-comments">
          <van-empty description="暂无评论，快来抢沙发吧~" />
        </div>

        <div v-else>
          <div v-for="comment in comments" :key="comment.id" class="comment-item">
            <van-image
              :src="comment.user?.avatar || '/default-avatar.png'"
              round
              width="32"
              height="32"
              fit="cover"
              class="comment-avatar"
            />
            <div class="comment-content">
              <div class="comment-user">{{ comment.user?.nickname || comment.user?.username }}</div>
              <div class="comment-text">{{ comment.content }}</div>
              <div class="comment-meta">
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                <div class="comment-actions">
                  <div class="comment-like" @click="handleCommentLike(comment)">
                    <van-icon
                      :name="comment.isLiked ? 'like' : 'like-o'"
                      :class="{ active: comment.isLiked }"
                      size="14"
                    />
                    <span v-if="comment.likeCount > 0">{{ comment.likeCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMoreComments" class="load-more">
            <van-button
              :loading="commentLoading"
              @click="loadMoreComments"
              type="default"
              size="small"
              block
            >
              {{ commentLoading ? '加载中...' : '加载更多评论' }}
            </van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论输入框 -->
    <van-popup
      v-model="showCommentInput"
      position="bottom"
      :style="{ height: '40%' }"
    >
      <div class="comment-input-container">
        <div class="comment-input-header">
          <span>写评论</span>
          <van-icon name="cross" @click="showCommentInput = false" />
        </div>
        <div class="comment-input-body">
          <van-field
            v-model="commentText"
            type="textarea"
            placeholder="说点什么..."
            rows="4"
            autosize
            maxlength="500"
            show-word-limit
          />
        </div>
        <div class="comment-input-footer">
          <van-button
            type="primary"
            @click="submitComment"
            :disabled="!commentText.trim()"
            block
          >
            发布评论
          </van-button>
        </div>
      </div>
    </van-popup>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showImagePreview } from 'vant'
import { getPhotoNoteDetail, likePhotoNote, unlikePhotoNote, collectPhotoNote, uncollectPhotoNote, getPhotoComments, addPhotoComment } from '@/api/photo'
import { followUser, unfollowUser, checkFollowing } from '@/api/user'
import { likeComment } from '@/api/comment'
import { getPrivateImageUrl } from '@/api/file'
import { formatRelativeTime } from '@/utils/time'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const noteDetail = ref(null)
const loading = ref(true)
const previewIndex = ref(0)
const privateImageUrls = ref([])

// 交互功能数据
const isFollowing = ref(false)
const comments = ref([])
const commentText = ref('')
const commentLoading = ref(false)
const commentPage = ref(1)
const commentSize = ref(10)
const hasMoreComments = ref(false)
const showCommentInput = ref(false)

// 计算属性
const previewImages = computed(() => {
  if (!noteDetail.value || !noteDetail.value.images) return []
  return noteDetail.value.images.map((image, index) => {
    // 优先使用私有URL，然后是原始URL，最后是缩略图URL
    const url = privateImageUrls.value[index] || image.url || image.thumbnailUrl
    console.log('预览图片URL:', url)
    return url
  })
})

const getPhotoGridClass = computed(() => {
  const count = noteDetail.value && noteDetail.value.images ? noteDetail.value.images.length : 0
  if (count === 1) return 'grid-1'
  if (count <= 4) return 'grid-2x2'
  return 'grid-3x3'
})

// 用户登录状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 是否是自己的笔记
const isOwnNote = computed(() => {
  return noteDetail.value && userStore.user && noteDetail.value.userId === userStore.user.id
})

// 处理标签和@用户高亮显示
const processedContent = computed(() => {
  if (!noteDetail.value || !noteDetail.value.content) return ''

  let content = noteDetail.value.content

  // 处理标签 #标签名称# 格式，显示为蓝色可点击
  content = content.replace(/#([^#]+)#/g, '<span class="tag-highlight" data-tag="$1">#$1#</span>')

  // 处理@用户提及 @用户昵称 格式，显示为橙色可点击
  content = content.replace(/@([^\s@]+)/g, '<span class="mention-highlight" data-mention="$1">@$1</span>')

  return content
})

// 方法
const loadNoteDetail = async () => {
  try {
    loading.value = true
    const noteId = route.params.id
    console.log('开始加载照片笔记详情，ID:', noteId)

    const response = await getPhotoNoteDetail(Number(noteId))
    console.log('照片笔记详情API响应:', response)

    // 处理不同的响应格式
    let data = null
    if (response && response.data) {
      data = response.data
    } else if (response && response.id) {
      data = response
    } else {
      console.error('API响应数据格式错误:', response)
      showToast('数据格式错误')
      return
    }

    noteDetail.value = data
    console.log('设置noteDetail.value:', noteDetail.value)

    // 处理私有图片URL
    if (noteDetail.value && noteDetail.value.images && noteDetail.value.images.length > 0) {
      console.log('开始处理私有图片URL，图片数量:', noteDetail.value.images.length)
      const urls = await Promise.all(
        noteDetail.value.images.map(async (image, index) => {
          try {
            console.log('处理第' + (index + 1) + '张图片:', image)
            const originalUrl = image.thumbnailUrl || image.url
            console.log('原始URL:', originalUrl)
            const processedUrl = await getPrivateImageUrl(originalUrl)
            console.log('处理后URL:', processedUrl)
            return processedUrl
          } catch (error) {
            console.error('获取私有图片URL失败:', error)
            return image.thumbnailUrl || image.url
          }
        })
      )
      privateImageUrls.value = urls
      console.log('所有私有图片URL处理完成:', privateImageUrls.value)
    } else {
      console.log('没有图片数据或图片数组为空')
    }

    // 检查关注状态
    if (isLoggedIn.value && !isOwnNote.value && noteDetail.value && noteDetail.value.userId) {
      try {
        const followRes = await checkFollowing(noteDetail.value.userId)
        if (followRes && followRes.data !== undefined) {
          isFollowing.value = followRes.data
        }
      } catch (error) {
        console.error('检查关注状态失败:', error)
      }
    }

    // 加载评论
    await loadComments()
  } catch (error) {
    console.error('加载照片笔记详情失败:', error)
    showToast('加载失败')
  } finally {
    loading.value = false
  }
}

const previewPhoto = (index) => {
  console.log('点击预览照片，索引:', index)
  console.log('noteDetail.value.images:', noteDetail.value?.images)
  console.log('privateImageUrls.value:', privateImageUrls.value)
  console.log('预览图片数组:', previewImages.value)
  console.log('当前图片URL:', previewImages.value[index])

  if (previewImages.value && previewImages.value.length > 0) {
    console.log('开始显示图片预览，起始索引:', index)
    showImagePreview({
      images: previewImages.value,
      startPosition: index,
      closeable: true,
      closeIconPosition: 'top-right',
      swipeDuration: 300,
      loop: true,
      maxZoom: 3,
      minZoom: 1/3,
      onClose: () => {
        console.log('图片预览已关闭')
      },
      onChange: (newIndex) => {
        console.log('预览图片切换到索引:', newIndex)
        previewIndex.value = newIndex
      }
    })
  } else {
    console.error('预览图片数组为空或未定义')
    showToast('图片加载中，请稍后再试')
  }
}

const onPreviewChange = (index) => {
  console.log('预览图片切换到索引:', index)
  previewIndex.value = index
}

const goToUserProfile = () => {
  router.push('/user/' + noteDetail.value.userId)
}

const shareNote = () => {
  // 使用原生分享API或复制链接的方式
  const shareUrl = window.location.origin + '/photo-note/' + noteDetail.value.id
  const shareTitle = noteDetail.value.title || '照片笔记'
  const shareText = noteDetail.value.content || '查看这个精彩的照片笔记'

  if (navigator.share) {
    // 使用原生分享API
    navigator.share({
      title: shareTitle,
      text: shareText,
      url: shareUrl
    }).then(() => {
      showToast('分享成功')
    }).catch((error) => {
      console.error('分享失败:', error)
      // 降级到复制链接
      copyToClipboard(shareUrl)
    })
  } else {
    // 降级到复制链接
    copyToClipboard(shareUrl)
  }
}

const copyToClipboard = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      showToast('链接已复制到剪贴板')
    }).catch(() => {
      showToast('复制失败，请手动复制')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      showToast('链接已复制到剪贴板')
    } catch (err) {
      showToast('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

const formatTime = (time) => {
  return formatRelativeTime(new Date(time))
}

// 加载评论
const loadComments = async () => {
  try {
    commentLoading.value = true
    const response = await getPhotoComments({
      photoId: Number(route.params.id),
      page: commentPage.value,
      size: commentSize.value
    })

    if (response && response.code === 200) {
      if (commentPage.value === 1) {
        comments.value = response.data.records || []
      } else {
        comments.value.push(...(response.data.records || []))
      }
      hasMoreComments.value = commentPage.value * commentSize.value < (response.data.total || 0)
    }
  } catch (error) {
    console.error('加载评论失败:', error)
  } finally {
    commentLoading.value = false
  }
}

// 点赞功能
const handleLike = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    if (noteDetail.value.isLiked) {
      await unlikePhotoNote(noteDetail.value.id)
      noteDetail.value.isLiked = false
      noteDetail.value.likeCount = Math.max((noteDetail.value.likeCount || 0) - 1, 0)
    } else {
      await likePhotoNote(noteDetail.value.id)
      noteDetail.value.isLiked = true
      noteDetail.value.likeCount = (noteDetail.value.likeCount || 0) + 1
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    showToast('操作失败')
  }
}

// 收藏功能
const handleCollect = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    if (noteDetail.value.isCollected) {
      await uncollectPhotoNote(noteDetail.value.id)
      noteDetail.value.isCollected = false
      noteDetail.value.collectCount = Math.max((noteDetail.value.collectCount || 0) - 1, 0)
    } else {
      await collectPhotoNote(noteDetail.value.id)
      noteDetail.value.isCollected = true
      noteDetail.value.collectCount = (noteDetail.value.collectCount || 0) + 1
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    showToast('操作失败')
  }
}

// 关注功能
const handleFollow = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    if (isFollowing.value) {
      await unfollowUser(noteDetail.value.userId)
      isFollowing.value = false
      showToast('已取消关注')
    } else {
      await followUser(noteDetail.value.userId)
      isFollowing.value = true
      showToast('关注成功')
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    showToast('操作失败')
  }
}

// 评论功能
const submitComment = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  if (!commentText.value.trim()) {
    showToast('请输入评论内容')
    return
  }

  try {
    const response = await addPhotoComment({
      photoId: Number(route.params.id),
      content: commentText.value.trim()
    })

    if (response && response.code === 200) {
      commentText.value = ''
      showCommentInput.value = false
      // 重新加载评论
      commentPage.value = 1
      await loadComments()
      // 更新评论数
      if (noteDetail.value) {
        noteDetail.value.commentCount = (noteDetail.value.commentCount || 0) + 1
      }
      showToast('评论成功')
    }
  } catch (error) {
    console.error('提交评论失败:', error)
    showToast('评论失败')
  }
}

// 点赞评论
const handleCommentLike = async (comment) => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    await likeComment(comment.id)
    comment.isLiked = !comment.isLiked
    comment.likeCount = comment.isLiked
      ? (comment.likeCount || 0) + 1
      : Math.max((comment.likeCount || 0) - 1, 0)
  } catch (error) {
    console.error('评论点赞失败:', error)
    showToast('操作失败')
  }
}

// 加载更多评论
const loadMoreComments = async () => {
  if (!hasMoreComments.value || commentLoading.value) return

  commentPage.value++
  await loadComments()
}

// 显示评论输入框
const showCommentBox = () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }
  showCommentInput.value = true
}

// 处理标签点击
const handleTagClick = (tagName) => {
  console.log('点击标签:', tagName)
  // 跳转到标签搜索页面或标签详情页面
  router.push('/search?tag=' + encodeURIComponent(tagName))
}

// 处理@用户提及点击
const handleMentionClick = (username) => {
  console.log('点击用户提及:', username)
  // 这里可以根据用户名查找用户ID，然后跳转到用户资料页面
  // 暂时使用用户名作为路径参数
  router.push('/user/profile/' + encodeURIComponent(username))
}

// 添加内容点击事件监听
const setupContentClickListeners = async () => {
  // 等待DOM更新完成
  await nextTick()

  // 等待一个额外的tick确保v-html内容已渲染
  setTimeout(() => {
    const contentElements = document.querySelectorAll('.note-content-text')
    console.log('找到内容元素数量:', contentElements.length)

    contentElements.forEach((contentElement, index) => {
      console.log(`设置第${index + 1}个内容元素的事件监听器`)

      // 检查是否有高亮元素
      const highlights = contentElement.querySelectorAll('.tag-highlight, .mention-highlight')
      console.log(`第${index + 1}个内容元素中找到${highlights.length}个高亮元素`)

      // 输出高亮元素的详细信息
      highlights.forEach((highlight, hIndex) => {
        console.log(`高亮元素${hIndex + 1}:`, highlight.outerHTML)
      })
    })
  }, 300)
}

// 内容点击处理函数
const handleContentClick = (event) => {
  const target = event.target
  console.log('点击了内容元素:', target)

  // 处理标签点击
  if (target.classList.contains('tag-highlight')) {
    const tagName = target.getAttribute('data-tag')
    console.log('点击标签:', tagName)
    if (tagName) {
      handleTagClick(tagName)
    }
  }

  // 处理用户提及点击
  if (target.classList.contains('mention-highlight')) {
    const username = target.getAttribute('data-mention')
    console.log('点击用户提及:', username)
    if (username) {
      handleMentionClick(username)
    }
  }
}

// 监听processedContent变化，输出调试信息
watch(processedContent, (newContent) => {
  if (newContent) {
    console.log('内容已更新，处理后的内容:', newContent)
    // 延迟设置事件监听器，确保DOM已更新
    nextTick(() => {
      setTimeout(() => {
        setupContentClickListeners()
      }, 100)
    })
  }
}, { flush: 'post' })

// 生命周期
onMounted(() => {
  console.log('PhotoNoteDetail组件已挂载')
  console.log('路由参数:', route.params)
  console.log('当前路径:', route.path)
  loadNoteDetail().then(() => {
    // 加载完成后设置内容点击监听
    setupContentClickListeners()
  })
})
</script>

<style scoped>
.photo-note-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.detail-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.note-content {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  margin-right: 12px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  cursor: pointer;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.follow-btn {
  margin-left: 12px;
}

.photo-section {
  padding: 0 16px;
}

.photo-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 16px;
}

.grid-1 {
  grid-template-columns: 1fr;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.content-section {
  padding: 0 16px 16px;
}

.note-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.note-content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 标签和用户提及高亮样式 */
.note-content-text :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.note-content-text :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.note-content-text :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.note-content-text :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}

.error-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}

/* 操作按钮区域 */
.action-section {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.action-item:hover {
  background-color: #f5f5f5;
}

.action-item .van-icon {
  margin-bottom: 4px;
  color: #666;
  transition: color 0.2s;
}

.action-item .van-icon.active {
  color: #ff6b6b;
}

.action-text {
  font-size: 12px;
  color: #666;
}

/* 评论区域 */
.comment-section {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.comment-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.comment-list {
  padding: 0 16px;
}

.comment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 8px;
  color: #666;
}

.no-comments {
  padding: 20px 0;
}

.comment-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-user {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.comment-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8px;
}

.comment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.comment-like {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.comment-like:hover {
  background-color: #f5f5f5;
}

.comment-like .van-icon {
  color: #666;
  transition: color 0.2s;
}

.comment-like .van-icon.active {
  color: #ff6b6b;
}

.comment-like span {
  font-size: 12px;
  color: #666;
}

.load-more {
  padding: 16px 0;
}

/* 评论输入框 */
.comment-input-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comment-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 600;
}

.comment-input-body {
  flex: 1;
  padding: 16px;
}

.comment-input-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 内容区域布局控制 */
/* 移动端：标题内容在照片下方 */
.note-content .content-section-top {
  display: none !important; /* 移动端隐藏标题内容在照片上方 */
}

.note-content .content-section-bottom {
  display: block !important; /* 移动端显示标题内容在照片下方 */
}

/* PC端适配 */
@media (min-width: 768px) {
  .photo-note-detail {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
  }

  .note-content {
    margin: 0;
    border-radius: 0;
  }

  .comment-section {
    margin: 0;
    border-radius: 0;
    border-top: 1px solid #f0f0f0;
  }

  .action-buttons {
    max-width: 400px;
    margin: 0 auto;
  }

  .action-item {
    flex-direction: row;
    gap: 8px;
  }

  .action-item .van-icon {
    margin-bottom: 0;
  }

  /* PC端布局：标题和内容在照片上方 */
  .note-content .content-section-top {
    display: block !important; /* PC端显示标题内容在照片上方 */
  }

  .note-content .content-section-bottom {
    display: none !important; /* PC端隐藏标题内容在照片下方 */
  }

  /* PC端照片网格优化 */
  .photo-grid.grid-1 {
    max-width: 500px;
    margin: 0 auto;
  }

  .photo-grid.grid-2x2 {
    max-width: 600px;
    margin: 0 auto;
  }

  .photo-grid.grid-3x3 {
    max-width: 600px;
    margin: 0 auto;
  }
}
</style>
