<template>
  <div class="photo-note-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="照片笔记"
      left-arrow
      @click-left="$router.back()"
      class="detail-navbar"
    >
      <template #right>
        <van-icon name="share-o" @click="shareNote" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#1989fa" vertical class="loading-container">
      加载中...
    </van-loading>

    <!-- 照片笔记内容 -->
    <div v-if="!loading && noteDetail" class="note-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <van-image
          :src="noteDetail.avatar"
          round
          width="40"
          height="40"
          fit="cover"
          class="user-avatar"
          @click="goToUserProfile"
        />
        <div class="user-details">
          <div class="user-name" @click="goToUserProfile">{{ noteDetail.nickname }}</div>
          <div class="publish-time">{{ formatTime(noteDetail.createdAt) }}</div>
        </div>
        <!-- 关注按钮 -->
        <van-button
          v-if="isLoggedIn && !isOwnNote"
          :type="isFollowing ? 'default' : 'primary'"
          size="small"
          round
          @click="handleFollow"
          class="follow-btn"
        >
          {{ isFollowing ? '已关注' : '关注' }}
        </van-button>
      </div>

      <!-- 标题和内容 - PC端在照片上方显示 -->
      <div class="content-section content-section-top">
        <h2 v-if="noteDetail.title" class="note-title">{{ noteDetail.title }}</h2>
        <div class="note-content-text" v-html="processedContent" @click="handleContentClick"></div>
      </div>

      <!-- 照片展示 -->
      <div class="photo-section">
        <div class="photo-grid" :class="getPhotoGridClass">
          <div
            v-for="(image, index) in noteDetail.images"
            :key="image.photoId"
            class="photo-item"
            @click="previewPhoto(index)"
          >
            <van-image
              :src="privateImageUrls[index] || image.thumbnailUrl || image.url"
              fit="cover"
              width="100%"
              height="100%"
              :alt="`照片${index + 1}`"
            >
              <template #loading>
                <van-loading type="spinner" size="20" />
              </template>
              <template #error>
                <div class="error-placeholder">
                  <van-icon name="photo-fail" size="24" />
                </div>
              </template>
            </van-image>
          </div>
        </div>
      </div>

      <!-- 标题和内容 - 移动端在照片下方显示 -->
      <div class="content-section content-section-bottom">
        <h2 v-if="noteDetail.title" class="note-title">{{ noteDetail.title }}</h2>
        <div class="note-content-text" v-html="processedContent" @click="handleContentClick"></div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <div class="action-item" @click="handleLike">
            <van-icon
              :name="noteDetail.isLiked ? 'like' : 'like-o'"
              :class="{ active: noteDetail.isLiked }"
              size="20"
            />
            <span class="action-text">{{ noteDetail.likeCount || 0 }}</span>
          </div>
          <div class="action-item" @click="handleCollect">
            <van-icon
              :name="noteDetail.isCollected ? 'star' : 'star-o'"
              :class="{ active: noteDetail.isCollected }"
              size="20"
            />
            <span class="action-text">{{ noteDetail.collectCount || 0 }}</span>
          </div>
          <div class="action-item" @click="scrollToCommentInput">
            <van-icon name="chat-o" size="20" />
            <span class="action-text">{{ noteDetail.commentCount || 0 }}</span>
          </div>
          <div class="action-item" @click="shareNote">
            <van-icon name="share-o" size="20" />
            <span class="action-text">分享</span>
          </div>
        </div>
      </div>

      <!-- 固定显示的评论输入区域 - 移动到操作按钮下方 -->
      <div class="comment-input-section">
        <div class="comment-input-header">
          <span>{{ replyTarget ? '回复评论' : '写评论' }}</span>
          <span v-if="replyTarget" class="clear-reply" @click="clearReplyTarget">
            <van-icon name="cross" size="14" />
          </span>
        </div>

        <!-- 回复目标显示 -->
        <div v-if="replyTarget" class="reply-target">
          <div class="reply-target-info">
            <span class="reply-label">回复</span>
            <span class="reply-user">{{ replyTarget.user?.nickname || replyTarget.user?.username }}</span>
            <span class="reply-content">{{ replyTarget.content.substring(0, 30) }}{{ replyTarget.content.length > 30 ? '...' : '' }}</span>
          </div>
          <van-icon name="cross" @click="clearReplyTarget" class="reply-close" />
        </div>

        <div class="comment-input-body">
          <van-field
            v-model="commentText"
            type="textarea"
            :placeholder="getCommentPlaceholder()"
            rows="2"
            autosize
            maxlength="500"
            show-word-limit
            :disabled="!isLoggedIn"
            @input="onCommentInput"
            @focus="handleInputFocus"
          />

          <!-- 实时预览评论内容 -->
          <div v-if="commentText.trim()" class="comment-preview">
            <div class="preview-label">预览：</div>
            <div class="preview-content" v-html="processCommentContent(commentText)" @click="handleContentClick"></div>
          </div>
        </div>

        <div class="comment-input-footer">
          <div class="comment-input-tips">
            <span class="tip-item">支持 #标签# 和 @用户名</span>
            <span v-if="replyTarget" class="tip-item reply-tip">回复将自动@被回复用户</span>
          </div>

          <van-button
            type="primary"
            @click="submitComment"
            :disabled="!isLoggedIn || !commentText.trim()"
            block
            size="small"
          >
            {{ isLoggedIn ? (replyTarget ? '发布回复' : '发布评论') : '请先登录后评论' }}
          </van-button>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <van-empty v-else-if="!loading && !noteDetail" description="照片笔记不存在或已被删除" />

    <!-- 评论区域 -->
    <div v-if="!loading && noteDetail" class="comment-section">
      <div class="comment-header">
        <h3>评论 ({{ noteDetail.commentCount || 0 }})</h3>
      </div>

      <!-- 评论列表 -->
      <div class="comment-list">
        <div v-if="commentLoading && comments.length === 0" class="comment-loading">
          <van-loading type="spinner" size="20" />
          <span>加载评论中...</span>
        </div>

        <div v-else-if="comments.length === 0" class="no-comments">
          <van-empty description="暂无评论，快来抢沙发吧~" />
        </div>

        <div v-else>
          <div v-for="comment in comments" :key="comment.id" class="comment-item">
            <van-image
              :src="comment.user?.avatar || '/default-avatar.png'"
              round
              width="32"
              height="32"
              fit="cover"
              class="comment-avatar"
            />
            <div class="comment-content">
              <div class="comment-user">{{ comment.user?.nickname || comment.user?.username }}</div>
              <div class="comment-text" v-html="processCommentContent(comment.content)" @click="handleContentClick"></div>
              <div class="comment-meta">
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                <div class="comment-actions">
                  <div class="comment-like" @click="handleCommentLike(comment)">
                    <van-icon
                      :name="comment.isLiked ? 'like' : 'like-o'"
                      :class="{ active: comment.isLiked }"
                      size="14"
                    />
                    <span v-if="comment.likeCount > 0">{{ comment.likeCount }}</span>
                  </div>
                  <div class="comment-reply" @click="replyToComment(comment)">
                    <van-icon name="chat-o" size="14" />
                    <span>回复</span>
                  </div>
                </div>
              </div>

              <!-- 回复列表 -->
              <div v-if="comment.replies && comment.replies.length > 0" class="reply-list">
                <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                  <van-image
                    :src="reply.user?.avatar || '/default-avatar.png'"
                    round
                    width="24"
                    height="24"
                    fit="cover"
                    class="reply-avatar"
                  />
                  <div class="reply-content">
                    <div class="reply-user">{{ reply.user?.nickname || reply.user?.username }}</div>
                    <div class="reply-text" v-html="processCommentContent(reply.content)" @click="handleContentClick"></div>
                    <div class="reply-meta">
                      <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                      <div class="reply-actions">
                        <div class="reply-like" @click="handleCommentLike(reply)">
                          <van-icon
                            :name="reply.isLiked ? 'like' : 'like-o'"
                            :class="{ active: reply.isLiked }"
                            size="12"
                          />
                          <span v-if="reply.likeCount > 0">{{ reply.likeCount }}</span>
                        </div>
                        <div class="reply-reply" @click="replyToComment(comment, reply)">
                          <van-icon name="chat-o" size="12" />
                          <span>回复</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMoreComments" class="load-more">
            <van-button
              :loading="commentLoading"
              @click="loadMoreComments"
              type="default"
              size="small"
              block
            >
              {{ commentLoading ? '加载中...' : '加载更多评论' }}
            </van-button>
          </div>
        </div>
      </div>

    </div>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showImagePreview } from 'vant'
import { getPhotoNoteDetail, likePhotoNote, unlikePhotoNote, collectPhotoNote, uncollectPhotoNote, getPhotoComments, addPhotoComment } from '@/api/photo'
import { followUser, unfollowUser, checkFollowing } from '@/api/user'
import { likeComment } from '@/api/comment'
import { getPrivateImageUrl } from '@/api/file'
import { formatRelativeTime } from '@/utils/time'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const noteDetail = ref(null)
const loading = ref(true)
const previewIndex = ref(0)
const privateImageUrls = ref([])

// 交互功能数据
const isFollowing = ref(false)
const comments = ref([])
const commentText = ref('')
const commentLoading = ref(false)
const commentPage = ref(1)
const commentSize = ref(10)
const hasMoreComments = ref(false)

// 回复功能数据
const replyTarget = ref(null) // 回复目标评论
const parentComment = ref(null) // 被回复的评论（用于回复回复）

// 计算属性
const previewImages = computed(() => {
  if (!noteDetail.value || !noteDetail.value.images) return []
  return noteDetail.value.images.map((image, index) => {
    // 优先使用私有URL，然后是原始URL，最后是缩略图URL
    const url = privateImageUrls.value[index] || image.url || image.thumbnailUrl
    console.log('预览图片URL:', url)
    return url
  })
})

const getPhotoGridClass = computed(() => {
  const count = noteDetail.value && noteDetail.value.images ? noteDetail.value.images.length : 0
  if (count === 1) return 'grid-1'
  if (count <= 4) return 'grid-2x2'
  return 'grid-3x3'
})

// 用户登录状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 是否是自己的笔记
const isOwnNote = computed(() => {
  return noteDetail.value && userStore.user && noteDetail.value.userId === userStore.user.id
})

// 处理标签和@用户高亮显示
const processedContent = computed(() => {
  if (!noteDetail.value || !noteDetail.value.content) return ''

  let content = noteDetail.value.content

  // 处理标签 #标签名称# 格式，显示为蓝色可点击
  content = content.replace(/#([^#]+)#/g, '<span class="tag-highlight" data-tag="$1">#$1#</span>')

  // 处理@用户提及 @用户昵称 格式，显示为橙色可点击
  content = content.replace(/@([^\s@]+)/g, '<span class="mention-highlight" data-mention="$1">@$1</span>')

  return content
})

// 处理评论内容的标签和@用户高亮显示
const processCommentContent = (content) => {
  if (!content) return ''

  let processedContent = content

  // 处理标签 #标签名称# 格式，显示为蓝色可点击
  processedContent = processedContent.replace(/#([^#]+)#/g, '<span class="tag-highlight" data-tag="$1">#$1#</span>')

  // 处理@用户提及 @用户昵称 格式，显示为橙色可点击
  processedContent = processedContent.replace(/@([^\s@]+)/g, '<span class="mention-highlight" data-mention="$1">@$1</span>')

  return processedContent
}

// 方法
const loadNoteDetail = async () => {
  try {
    loading.value = true
    const noteId = route.params.id
    console.log('开始加载照片笔记详情，ID:', noteId)

    const response = await getPhotoNoteDetail(Number(noteId))
    console.log('照片笔记详情API响应:', response)

    // 处理不同的响应格式
    let data = null
    if (response && response.data) {
      data = response.data
    } else if (response && response.id) {
      data = response
    } else {
      console.error('API响应数据格式错误:', response)
      showToast('数据格式错误')
      return
    }

    noteDetail.value = data
    console.log('设置noteDetail.value:', noteDetail.value)

    // 处理私有图片URL
    if (noteDetail.value && noteDetail.value.images && noteDetail.value.images.length > 0) {
      console.log('开始处理私有图片URL，图片数量:', noteDetail.value.images.length)
      const urls = await Promise.all(
        noteDetail.value.images.map(async (image, index) => {
          try {
            console.log('处理第' + (index + 1) + '张图片:', image)
            const originalUrl = image.thumbnailUrl || image.url
            console.log('原始URL:', originalUrl)
            const processedUrl = await getPrivateImageUrl(originalUrl)
            console.log('处理后URL:', processedUrl)
            return processedUrl
          } catch (error) {
            console.error('获取私有图片URL失败:', error)
            return image.thumbnailUrl || image.url
          }
        })
      )
      privateImageUrls.value = urls
      console.log('所有私有图片URL处理完成:', privateImageUrls.value)
    } else {
      console.log('没有图片数据或图片数组为空')
    }

    // 检查关注状态
    if (isLoggedIn.value && !isOwnNote.value && noteDetail.value && noteDetail.value.userId) {
      try {
        const followRes = await checkFollowing(noteDetail.value.userId)
        if (followRes && followRes.data !== undefined) {
          isFollowing.value = followRes.data
        }
      } catch (error) {
        console.error('检查关注状态失败:', error)
      }
    }

    // 加载评论
    await loadComments()
  } catch (error) {
    console.error('加载照片笔记详情失败:', error)
    showToast('加载失败')
  } finally {
    loading.value = false
  }
}

const previewPhoto = (index) => {
  console.log('点击预览照片，索引:', index)
  console.log('noteDetail.value.images:', noteDetail.value?.images)
  console.log('privateImageUrls.value:', privateImageUrls.value)
  console.log('预览图片数组:', previewImages.value)
  console.log('当前图片URL:', previewImages.value[index])

  if (previewImages.value && previewImages.value.length > 0) {
    console.log('开始显示图片预览，起始索引:', index)
    showImagePreview({
      images: previewImages.value,
      startPosition: index,
      closeable: true,
      closeIconPosition: 'top-right',
      swipeDuration: 300,
      loop: true,
      maxZoom: 3,
      minZoom: 1/3,
      onClose: () => {
        console.log('图片预览已关闭')
      },
      onChange: (newIndex) => {
        console.log('预览图片切换到索引:', newIndex)
        previewIndex.value = newIndex
      }
    })
  } else {
    console.error('预览图片数组为空或未定义')
    showToast('图片加载中，请稍后再试')
  }
}

const onPreviewChange = (index) => {
  console.log('预览图片切换到索引:', index)
  previewIndex.value = index
}

const goToUserProfile = () => {
  router.push('/user/' + noteDetail.value.userId)
}

const shareNote = () => {
  // 使用原生分享API或复制链接的方式
  const shareUrl = window.location.origin + '/photo-note/' + noteDetail.value.id
  const shareTitle = noteDetail.value.title || '照片笔记'
  const shareText = noteDetail.value.content || '查看这个精彩的照片笔记'

  if (navigator.share) {
    // 使用原生分享API
    navigator.share({
      title: shareTitle,
      text: shareText,
      url: shareUrl
    }).then(() => {
      showToast('分享成功')
    }).catch((error) => {
      console.error('分享失败:', error)
      // 降级到复制链接
      copyToClipboard(shareUrl)
    })
  } else {
    // 降级到复制链接
    copyToClipboard(shareUrl)
  }
}

const copyToClipboard = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      showToast('链接已复制到剪贴板')
    }).catch(() => {
      showToast('复制失败，请手动复制')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      showToast('链接已复制到剪贴板')
    } catch (err) {
      showToast('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

const formatTime = (time) => {
  return formatRelativeTime(new Date(time))
}

// 加载评论
const loadComments = async () => {
  try {
    commentLoading.value = true
    const response = await getPhotoComments({
      photoId: Number(route.params.id),
      page: commentPage.value,
      size: commentSize.value
    })

    if (response && response.code === 200) {
      if (commentPage.value === 1) {
        comments.value = response.data.records || []
      } else {
        comments.value.push(...(response.data.records || []))
      }
      hasMoreComments.value = commentPage.value * commentSize.value < (response.data.total || 0)
    }
  } catch (error) {
    console.error('加载评论失败:', error)
  } finally {
    commentLoading.value = false
  }
}

// 点赞功能
const handleLike = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    if (noteDetail.value.isLiked) {
      await unlikePhotoNote(noteDetail.value.id)
      noteDetail.value.isLiked = false
      noteDetail.value.likeCount = Math.max((noteDetail.value.likeCount || 0) - 1, 0)
    } else {
      await likePhotoNote(noteDetail.value.id)
      noteDetail.value.isLiked = true
      noteDetail.value.likeCount = (noteDetail.value.likeCount || 0) + 1
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    showToast('操作失败')
  }
}

// 收藏功能
const handleCollect = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    if (noteDetail.value.isCollected) {
      await uncollectPhotoNote(noteDetail.value.id)
      noteDetail.value.isCollected = false
      noteDetail.value.collectCount = Math.max((noteDetail.value.collectCount || 0) - 1, 0)
    } else {
      await collectPhotoNote(noteDetail.value.id)
      noteDetail.value.isCollected = true
      noteDetail.value.collectCount = (noteDetail.value.collectCount || 0) + 1
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    showToast('操作失败')
  }
}

// 关注功能
const handleFollow = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    if (isFollowing.value) {
      await unfollowUser(noteDetail.value.userId)
      isFollowing.value = false
      showToast('已取消关注')
    } else {
      await followUser(noteDetail.value.userId)
      isFollowing.value = true
      showToast('关注成功')
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    showToast('操作失败')
  }
}

// 评论输入处理
const onCommentInput = (value) => {
  console.log('评论输入内容:', value)
  // 这里可以添加实时的标签和@用户提及建议功能
}

// 提取评论中的标签和@用户提及
const extractCommentData = (content) => {
  const tags = []
  const mentions = []

  // 提取标签
  const tagMatches = content.match(/#([^#]+)#/g)
  if (tagMatches) {
    tagMatches.forEach(match => {
      const tagName = match.slice(1, -1) // 去掉前后的#号
      if (tagName && !tags.includes(tagName)) {
        tags.push(tagName)
      }
    })
  }

  // 提取@用户提及
  const mentionMatches = content.match(/@([^\s@]+)/g)
  if (mentionMatches) {
    mentionMatches.forEach(match => {
      const username = match.slice(1) // 去掉@号
      if (username && !mentions.includes(username)) {
        mentions.push(username)
      }
    })
  }

  return { tags, mentions }
}

// 评论功能
const submitComment = async () => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  if (!commentText.value.trim()) {
    showToast('请输入评论内容')
    return
  }

  try {
    // 提取评论中的标签和@用户提及
    const { tags, mentions } = extractCommentData(commentText.value.trim())
    console.log('评论中提取的标签:', tags)
    console.log('评论中提取的@用户提及:', mentions)

    // 构建评论数据
    const commentData = {
      photoId: Number(route.params.id),
      content: commentText.value.trim(),
      tags: tags.length > 0 ? tags : undefined,
      mentions: mentions.length > 0 ? mentions : undefined
    }

    // 如果是回复评论，添加回复相关信息
    if (replyTarget.value) {
      commentData.parentId = parentComment.value ? parentComment.value.id : replyTarget.value.id
      commentData.replyToUserId = replyTarget.value.user?.id
      commentData.replyToUsername = replyTarget.value.user?.nickname || replyTarget.value.user?.username

      console.log('回复评论数据:', {
        parentId: commentData.parentId,
        replyToUserId: commentData.replyToUserId,
        replyToUsername: commentData.replyToUsername
      })
    }

    const response = await addPhotoComment(commentData)

    if (response && response.code === 200) {
      const isReply = !!replyTarget.value
      commentText.value = ''
      clearReplyTarget()

      // 重新加载评论
      commentPage.value = 1
      await loadComments()

      // 更新评论数
      if (noteDetail.value) {
        noteDetail.value.commentCount = (noteDetail.value.commentCount || 0) + 1
      }

      showToast(isReply ? '回复成功' : '评论成功')
    } else {
      console.error('评论提交失败，响应:', response)
      showToast('评论失败，请重试')
    }
  } catch (error) {
    console.error('提交评论失败:', error)
    showToast('评论失败，请检查网络连接')
  }
}

// 点赞评论
const handleCommentLike = async (comment) => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  try {
    await likeComment(comment.id)
    comment.isLiked = !comment.isLiked
    comment.likeCount = comment.isLiked
      ? (comment.likeCount || 0) + 1
      : Math.max((comment.likeCount || 0) - 1, 0)
  } catch (error) {
    console.error('评论点赞失败:', error)
    showToast('操作失败')
  }
}

// 加载更多评论
const loadMoreComments = async () => {
  if (!hasMoreComments.value || commentLoading.value) return

  commentPage.value++
  await loadComments()
}

// 获取评论输入框占位符文本
const getCommentPlaceholder = () => {
  if (!isLoggedIn.value) {
    return '请先登录后发表评论...'
  }

  if (replyTarget.value) {
    const username = replyTarget.value.user?.nickname || replyTarget.value.user?.username
    return `回复 ${username}...支持#标签#和@用户提及`
  }

  return '说点什么...支持#标签#和@用户提及'
}

// 处理输入框获得焦点
const handleInputFocus = () => {
  if (!isLoggedIn.value) {
    showToast('请先登录后发表评论')
    return
  }
  console.log('评论输入框获得焦点')
}

// 滚动到评论输入区域
const scrollToCommentInput = () => {
  nextTick(() => {
    const commentInputSection = document.querySelector('.comment-input-section')
    if (commentInputSection) {
      commentInputSection.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  })
}

// 回复评论
const replyToComment = (comment, reply = null) => {
  if (!isLoggedIn.value) {
    showToast('请先登录')
    return
  }

  // 设置回复目标
  replyTarget.value = reply || comment
  parentComment.value = reply ? comment : null

  // 自动添加@用户名到评论文本
  const username = replyTarget.value.user?.nickname || replyTarget.value.user?.username
  if (username) {
    commentText.value = `@${username} `
  }

  // 滚动到评论输入区域
  nextTick(() => {
    const commentInputSection = document.querySelector('.comment-input-section')
    if (commentInputSection) {
      commentInputSection.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  })

  console.log('回复评论:', {
    replyTarget: replyTarget.value,
    parentComment: parentComment.value,
    username
  })
}

// 清除回复目标
const clearReplyTarget = () => {
  replyTarget.value = null
  parentComment.value = null
  commentText.value = ''
}



// 处理标签点击
const handleTagClick = (tagName) => {
  console.log('点击标签:', tagName)
  // 跳转到标签搜索页面或标签详情页面
  router.push('/search?tag=' + encodeURIComponent(tagName))
}

// 处理@用户提及点击
const handleMentionClick = (username) => {
  console.log('点击用户提及:', username)
  // 这里可以根据用户名查找用户ID，然后跳转到用户资料页面
  // 暂时使用用户名作为路径参数
  router.push('/user/profile/' + encodeURIComponent(username))
}

// 调试内容处理结果
const debugContentProcessing = () => {
  console.log('=== 内容处理调试信息 ===')
  console.log('noteDetail.value:', noteDetail.value)
  console.log('原始内容:', noteDetail.value?.content)
  console.log('处理后内容:', processedContent.value)

  // 检查DOM中的高亮元素
  nextTick(() => {
    setTimeout(() => {
      const contentElements = document.querySelectorAll('.note-content-text')
      console.log('找到内容元素数量:', contentElements.length)

      contentElements.forEach((element, index) => {
        console.log(`内容元素${index + 1} HTML:`, element.innerHTML)
        const highlights = element.querySelectorAll('.tag-highlight, .mention-highlight')
        console.log(`找到${highlights.length}个高亮元素`)
        highlights.forEach((highlight, hIndex) => {
          console.log(`高亮元素${hIndex + 1}:`, highlight.outerHTML)
        })
      })
    }, 100)
  })
}

// 内容点击处理函数
const handleContentClick = (event) => {
  const target = event.target
  console.log('点击了内容元素:', target)

  // 处理标签点击
  if (target.classList.contains('tag-highlight')) {
    const tagName = target.getAttribute('data-tag')
    console.log('点击标签:', tagName)
    if (tagName) {
      handleTagClick(tagName)
    }
  }

  // 处理用户提及点击
  if (target.classList.contains('mention-highlight')) {
    const username = target.getAttribute('data-mention')
    console.log('点击用户提及:', username)
    if (username) {
      handleMentionClick(username)
    }
  }
}

// 监听processedContent变化，输出调试信息
watch(processedContent, (newContent) => {
  if (newContent) {
    console.log('内容已更新，处理后的内容:', newContent)
    debugContentProcessing()
  }
}, { flush: 'post' })

// 生命周期
onMounted(() => {
  console.log('PhotoNoteDetail组件已挂载')
  console.log('路由参数:', route.params)
  console.log('当前路径:', route.path)
  loadNoteDetail().then(() => {
    // 加载完成后调试内容处理
    debugContentProcessing()
  })
})
</script>

<style scoped>
.photo-note-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.detail-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.note-content {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  margin-right: 12px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  cursor: pointer;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.follow-btn {
  margin-left: 12px;
}

.photo-section {
  padding: 0 16px;
}

.photo-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 16px;
}

.grid-1 {
  grid-template-columns: 1fr;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.content-section {
  padding: 0 16px 16px;
}

.note-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.note-content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 标签和用户提及高亮样式 */
.note-content-text :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.note-content-text :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.note-content-text :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.note-content-text :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}

.error-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}

/* 操作按钮区域 */
.action-section {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.action-item:hover {
  background-color: #f5f5f5;
}

.action-item .van-icon {
  margin-bottom: 4px;
  color: #666;
  transition: color 0.2s;
}

.action-item .van-icon.active {
  color: #ff6b6b;
}

.action-text {
  font-size: 12px;
  color: #666;
}

/* 评论区域 */
.comment-section {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.comment-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.comment-list {
  padding: 0 16px;
}

.comment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 8px;
  color: #666;
}

.no-comments {
  padding: 20px 0;
}

.comment-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-user {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.comment-text {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8px;
}

.comment-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.comment-like {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.comment-like:hover {
  background-color: #f5f5f5;
}

.comment-like .van-icon {
  color: #666;
  transition: color 0.2s;
}

.comment-like .van-icon.active {
  color: #ff6b6b;
}

.comment-like span {
  font-size: 12px;
  color: #666;
}

/* 评论回复按钮样式 */
.comment-reply {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.comment-reply:hover {
  background-color: #f5f5f5;
}

.comment-reply .van-icon {
  color: #1989fa;
  transition: color 0.2s;
}

.comment-reply span {
  font-size: 12px;
  color: #1989fa;
}

/* 回复列表样式 */
.reply-list {
  margin-top: 12px;
  padding-left: 20px;
  border-left: 2px solid #f0f0f0;
}

.reply-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-avatar {
  margin-right: 8px;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.reply-user {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.reply-text {
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 6px;
}

.reply-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reply-time {
  font-size: 11px;
  color: #999;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reply-like,
.reply-reply {
  display: flex;
  align-items: center;
  gap: 2px;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.reply-like:hover,
.reply-reply:hover {
  background-color: #f5f5f5;
}

.reply-like .van-icon {
  color: #666;
  transition: color 0.2s;
}

.reply-like .van-icon.active {
  color: #ff6b6b;
}

.reply-reply .van-icon {
  color: #1989fa;
}

.reply-like span,
.reply-reply span {
  font-size: 11px;
  color: #666;
}

.reply-reply span {
  color: #1989fa;
}

/* 回复目标显示样式 */
.reply-target {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.reply-target-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.reply-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.reply-user {
  font-size: 12px;
  color: #1989fa;
  font-weight: 600;
}

.reply-content {
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reply-close {
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.reply-close:hover {
  background-color: #e8e8e8;
}

/* 回复提示样式 */
.reply-tip {
  background-color: #e8f4ff !important;
  color: #1989fa !important;
}

.load-more {
  padding: 16px 0;
}

/* 固定显示的评论输入区域样式 - 紧凑版本 */
.comment-input-section {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
}

.comment-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.clear-reply {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #999;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.clear-reply:hover {
  background-color: #f5f5f5;
}

.comment-input-body {
  margin-bottom: 12px;
}

.comment-input-footer {
  margin-top: 12px;
}

/* 评论预览样式 */
.comment-preview {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #1989fa;
}

.preview-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 评论输入提示 - 紧凑版本 */
.comment-input-tips {
  margin-bottom: 8px;
  text-align: center;
}

.tip-item {
  font-size: 11px;
  color: #999;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  margin: 0 2px;
}

/* 评论内容中的标签和用户提及高亮样式 */
.comment-text :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.comment-text :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.comment-text :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.comment-text :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}

/* 评论预览中的标签和用户提及高亮样式 */
.preview-content :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.preview-content :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.preview-content :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.preview-content :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}

/* 内容区域布局控制 */
/* 移动端：标题内容在照片下方 */
.note-content .content-section-top {
  display: none !important; /* 移动端隐藏标题内容在照片上方 */
}

.note-content .content-section-bottom {
  display: block !important; /* 移动端显示标题内容在照片下方 */
}

/* PC端适配 */
@media (min-width: 768px) {
  .photo-note-detail {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
  }

  .note-content {
    margin: 0;
    border-radius: 0;
  }

  .comment-section {
    margin: 0;
    border-radius: 0;
    border-top: 1px solid #f0f0f0;
  }

  .action-buttons {
    max-width: 400px;
    margin: 0 auto;
  }

  .action-item {
    flex-direction: row;
    gap: 8px;
  }

  .action-item .van-icon {
    margin-bottom: 0;
  }

  /* PC端布局：标题和内容在照片上方 */
  .note-content .content-section-top {
    display: block !important; /* PC端显示标题内容在照片上方 */
  }

  .note-content .content-section-bottom {
    display: none !important; /* PC端隐藏标题内容在照片下方 */
  }

  /* PC端照片网格优化 */
  .photo-grid.grid-1 {
    max-width: 500px;
    margin: 0 auto;
  }

  .photo-grid.grid-2x2 {
    max-width: 600px;
    margin: 0 auto;
  }

  .photo-grid.grid-3x3 {
    max-width: 600px;
    margin: 0 auto;
  }
}
</style>
