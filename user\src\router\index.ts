import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: { title: '首页', requiresAuth: false }
  },
  // 认证相关路由
  {
    path: '/auth',
    name: 'Auth',
    redirect: '/auth/login',
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/Login.vue'),
        meta: { title: '登录', requiresAuth: false }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/register/index.vue'),
        meta: { title: '注册', requiresAuth: false }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: () => import('@/views/auth/forgot-password/index.vue'),
        meta: { title: '忘记密码', requiresAuth: false }
      }
    ]
  },
  // 旧路径重定向
  {
    path: '/login',
    redirect: '/auth/login'
  },
  {
    path: '/register',
    redirect: '/auth/register'
  },
  // 照片相关路由
  {
    path: '/photo',
    children: [
      {
        path: '',
        name: 'PhotoList',
        component: () => import('@/views/photo/index.vue'),
        meta: { title: '照片列表', requiresAuth: false }
      },
      {
        path: 'detail/:id',
        name: 'PhotoDetail',
        component: () => import('@/views/photo/detail.vue'),
        meta: { title: '照片详情', requiresAuth: false }
      },
      // 兼容旧路径
      {
        path: ':id',
        redirect: to => {
          // 如果 id 不是 'detail'，则重定向到新路径
          if (to.params.id !== 'detail') {
            return { path: `/photo/detail/${to.params.id}` }
          }
        }
      }
    ]
  },

  // 照片笔记相关路由
  {
    path: '/photo-note',
    children: [
      {
        path: ':id',
        name: 'PhotoNoteDetail',
        component: () => import('@/views/photo-note/PhotoNoteDetail.vue'),
        meta: { title: '照片笔记详情', requiresAuth: false }
      }
    ]
  },

  // 发布相关路由
  {
    path: '/publish',
    children: [
      {
        path: 'photo-note',
        name: 'PublishPhotoNote',
        component: () => import('@/views/publish/PhotoNotePublish.vue'),
        meta: { title: '发布照片笔记', requiresAuth: true }
      }
    ]
  },

  // 标签相关路由
  {
    path: '/tag',
    children: [
      {
        path: ':tagName',
        name: 'TagSearch',
        component: () => import('@/views/tag/TagSearch.vue'),
        meta: { title: '标签搜索', requiresAuth: false }
      }
    ]
  },

  // 用户相关路由
  {
    path: '/user',
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/profile/index.vue'),
        meta: { title: '个人中心', requiresAuth: true }
      },
      {
        path: ':id',
        name: 'UserDetail',
        component: () => import('@/views/user/detail.vue'),
        meta: { title: '用户主页', requiresAuth: false }
      }
    ]
  },
  // 搜索路由
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/search/index.vue'),
    meta: { title: '搜索', requiresAuth: false }
  },
  // 上传路由
  {
    path: '/upload',
    name: 'Upload',
    component: () => import('@/views/upload/index.vue'),
    meta: { title: '上传照片', requiresAuth: true }
  },
  // 设置路由
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/settings/index.vue'),
    meta: { title: '设置', requiresAuth: true }
  },
  {
    path: '/settings/profile',
    name: 'ProfileSettings',
    component: () => import('@/views/settings/profile.vue'),
    meta: { title: '个人资料', requiresAuth: true }
  },
  // 通知路由
  {
    path: '/notifications',
    name: 'Notifications',
    component: () => import('@/views/notification/index.vue'),
    meta: { title: '通知', requiresAuth: true }
  },
  // 消息路由
  {
    path: '/messages',
    name: 'Messages',
    component: () => import('@/views/messages/index.vue'),
    meta: { title: '消息', requiresAuth: true }
  },
  {
    path: '/messages/chat/:id',
    name: 'Chat',
    component: () => import('@/views/messages/chat.vue'),
    meta: { title: '聊天', requiresAuth: true }
  },
  // 发现路由
  {
    path: '/explore',
    name: 'Explore',
    component: () => import('@/views/home/<USER>'), // 暂时使用首页作为发现页
    meta: { title: '发现', requiresAuth: false }
  },
  // 微信回调路由
  {
    path: '/wechat-callback',
    name: 'WechatCallback',
    component: () => import('@/views/auth/WechatCallback.vue'),
    meta: { title: '微信登录', requiresAuth: false }
  },
  // QQ回调路由
  {
    path: '/qq-callback',
    name: 'QQCallback',
    component: () => import('@/views/auth/QQCallback.vue'),
    meta: { title: 'QQ登录', requiresAuth: false }
  },
  // 测试路由
  {
    path: '/test/api',
    name: 'ApiTest',
    component: () => import('@/views/test/ApiTest.vue'),
    meta: { title: 'API测试', requiresAuth: false }
  },
  {
    path: '/test/upload',
    name: 'UploadTest',
    component: () => import('@/views/test/UploadTest.vue'),
    meta: { title: '上传功能测试', requiresAuth: false }
  },
  {
    path: '/test/photo-note-detail',
    name: 'PhotoNoteDetailTest',
    component: () => import('@/views/test/PhotoNoteDetailTest.vue'),
    meta: { title: '照片笔记详情测试', requiresAuth: false }
  },
  {
    path: '/test/behavior-record',
    name: 'BehaviorRecordTest',
    component: () => import('@/views/test/BehaviorRecordTest.vue'),
    meta: { title: '行为记录接口测试', requiresAuth: false }
  },
  {
    path: '/test/home-data',
    name: 'HomeDataTest',
    component: () => import('@/views/test/HomeDataTest.vue'),
    meta: { title: '首页数据测试', requiresAuth: false }
  },
  {
    path: '/test/layout',
    name: 'LayoutTest',
    component: () => import('@/views/test/LayoutTest.vue'),
    meta: { title: '布局和标签测试', requiresAuth: false }
  },
  {
    path: '/test/comment',
    name: 'CommentTest',
    component: () => import('@/views/test/CommentTest.vue'),
    meta: { title: '评论功能测试', requiresAuth: false }
  },
  // 错误页面
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在', requiresAuth: false }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - PhotoTagMoment` : 'PhotoTagMoment'

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    // 检查token是否存在
    const token = localStorage.getItem('token')
    if (!token) {
      console.log(`路由守卫: ${to.path} 需要登录，但没有token，重定向到登录页面`)
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }

    // 检查用户状态
    const userStore = useUserStore()
    if (!userStore.isLoggedIn) {
      console.log(`路由守卫: ${to.path} 需要登录，但用户未登录，重定向到登录页面`)
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }
  }

  next()
})

export default router
